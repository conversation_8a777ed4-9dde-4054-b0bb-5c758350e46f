{"tasks": [{"id": "6371b74e-a2a2-4cb7-85f1-c2723797f437", "name": "SQL 查询验证与测试", "description": "在数据库中测试修改后的 SQL 查询逻辑，验证过滤 generate_source=1 的效果，确保查询结果符合预期", "notes": "重点验证批量生成报告不再影响重复检查结果", "status": "completed", "dependencies": [], "createdAt": "2025-08-20T06:37:00.958Z", "updatedAt": "2025-08-20T06:40:04.677Z", "relatedFiles": [{"path": "aqsoc-monitor/src/main/resources/mapper/ffsafe/FfsafeScanReportTaskRelationMapper.xml", "type": "TO_MODIFY", "description": "需要修改的 checkExistingReports 查询", "lineStart": 61, "lineEnd": 67}], "implementationGuide": "1. 构建新的 SQL 查询：SELECT DISTINCT rel.task_summary_id FROM ffsafe_scan_report_task_relation rel INNER JOIN ffsafe_scan_report_record r ON rel.scan_report_record_id = r.id WHERE rel.task_summary_id IN (...) AND r.generate_source = 1\n2. 使用已知测试数据（任务ID 801, 805）进行验证\n3. 对比修改前后的查询结果差异\n4. 确保只返回有单条生成报告的任务ID", "verificationCriteria": "SQL 查询能正确过滤 generate_source=1 的记录，测试数据验证通过，查询性能良好", "analysisResult": "修改 ScanTaskReport 类的 checkDuplicateReports 方法，解决批量报告生成干扰单条报告重复检查的问题。核心是修改 checkExistingReports SQL 查询，添加对 generate_source=1 的过滤条件。", "summary": "SQL 查询验证完成！新查询逻辑正确：✅ 能正确过滤 generate_source=1 的记录 ✅ 测试数据验证通过（任务801、805有单条生成报告被返回，任务799只有批量生成报告被正确过滤） ✅ 查询性能良好，使用索引优化 ✅ MyBatis foreach 语法兼容性确认", "completedAt": "2025-08-20T06:40:04.672Z"}, {"id": "4d962059-4b0c-4e9e-b4cc-c465e7ca726a", "name": "修改 Mapper XML 文件", "description": "修改 FfsafeScanReportTaskRelationMapper.xml 中的 checkExistingReports 查询，添加 INNER JOIN ffsafe_scan_report_record 表和 generate_source=1 的过滤条件", "notes": "确保 MyBatis 语法正确，保持向后兼容性", "status": "pending", "dependencies": [{"taskId": "6371b74e-a2a2-4cb7-85f1-c2723797f437"}], "createdAt": "2025-08-20T06:37:00.958Z", "updatedAt": "2025-08-20T06:37:00.958Z", "relatedFiles": [{"path": "aqsoc-monitor/src/main/resources/mapper/ffsafe/FfsafeScanReportTaskRelationMapper.xml", "type": "TO_MODIFY", "description": "修改 checkExistingReports 查询实现", "lineStart": 61, "lineEnd": 67}], "implementationGuide": "1. 打开 FfsafeScanReportTaskRelationMapper.xml 文件\n2. 定位到 checkExistingReports 查询（第61-67行）\n3. 修改 SQL 查询：\n   - 添加表别名 rel\n   - 添加 INNER JOIN ffsafe_scan_report_record r ON rel.scan_report_record_id = r.id\n   - 添加 WHERE 条件 AND r.generate_source = 1\n4. 保持原有的 foreach 循环结构不变", "verificationCriteria": "XML 文件语法正确，SQL 查询逻辑符合设计要求，MyBatis 映射配置无误", "analysisResult": "修改 ScanTaskReport 类的 checkDuplicateReports 方法，解决批量报告生成干扰单条报告重复检查的问题。核心是修改 checkExistingReports SQL 查询，添加对 generate_source=1 的过滤条件。"}, {"id": "ca7be32b-e29a-4fcb-8157-12e32b8c36c4", "name": "功能验证与测试", "description": "验证修改后的 checkDuplicateReports 方法是否正确过滤批量生成报告，确保单条生成报告的重复检查逻辑正常工作", "notes": "重点验证批量生成报告不再干扰单条生成的重复检查", "status": "pending", "dependencies": [{"taskId": "4d962059-4b0c-4e9e-b4cc-c465e7ca726a"}], "createdAt": "2025-08-20T06:37:00.958Z", "updatedAt": "2025-08-20T06:37:00.958Z", "relatedFiles": [{"path": "aqsoc-monitor/src/main/java/com/ruoyi/ffsafe/scantaskapi/service/impl/FfsafeScanReportTaskRelationServiceImpl.java", "type": "REFERENCE", "description": "checkDuplicateReports 方法实现", "lineStart": 254, "lineEnd": 283}, {"path": "aqsoc-monitor/src/main/java/com/ruoyi/ffsafe/scantaskapi/event/ScanTaskReport.java", "type": "REFERENCE", "description": "createBatchTaskReport 方法中的重复检查调用", "lineStart": 125, "lineEnd": 135}], "implementationGuide": "1. 检查 FfsafeScanReportTaskRelationServiceImpl.checkDuplicateReports 方法调用\n2. 验证修改后的查询是否正确返回结果\n3. 测试场景：\n   - 只有单条生成报告的任务：应该返回任务ID\n   - 只有批量生成报告的任务：不应该返回任务ID\n   - 同时有单条和批量生成报告的任务：应该返回任务ID\n4. 确保 ScanTaskReport.createBatchTaskReport 方法的重复检查逻辑正常", "verificationCriteria": "重复检查逻辑正确，批量生成报告不再干扰单条生成检查，所有测试场景验证通过", "analysisResult": "修改 ScanTaskReport 类的 checkDuplicateReports 方法，解决批量报告生成干扰单条报告重复检查的问题。核心是修改 checkExistingReports SQL 查询，添加对 generate_source=1 的过滤条件。"}]}